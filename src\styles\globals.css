@import "tailwindcss";

:root {
  --background: #f8f9fa;
  --foreground: #333333;
  /* Rose Gold Theme */
  --rose-gold: #b76e79;
  --rose-gold-light: #d4a1a9;
  --rose-gold-dark: #9a5a64;
  --rose-gold-bg: #fdf0f2;
  --complementary-dark: #4a3e42;
  --complementary-light: #f7e8ea;
  --accent: #e5c1c6;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Force light mode regardless of system preference */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #f8f9fa;
    --foreground: #333333;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Override Tailwind dark mode classes */
.dark\:bg-\[\#1a1a1a\]:hover {
  background-color: #f0f0f0 !important;
}

.dark\:hover\:bg-\[\#ccc\]:hover {
  background-color: #e0e0e0 !important;
}

.dark\:border-white\/\[\.145\] {
  border-color: rgba(0, 0, 0, 0.145) !important;
}

.dark\:invert {
  filter: none !important;
}

/* Light theme for all pages */
.bg-gray-50 {
  background-color: #ffffff;
}

/* Ensure all cards and containers have light backgrounds */
.bg-white {
  background-color: #ffffff;
}

/* Ensure text is readable on light backgrounds */
.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-800 {
  color: #1f2937;
}

.text-gray-900 {
  color: #111827;
}

/* Process Steps Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes planeRight {
  0% {
    opacity: 0;
    transform: translate(-100%, -50%) translateY(5px);
  }
  10% {
    opacity: 1;
    transform: translate(-80%, -50%) translateY(0);
  }
  20% {
    transform: translate(-60%, -50%) translateY(-5px);
  }
  30% {
    transform: translate(-40%, -50%) translateY(0);
  }
  40% {
    transform: translate(-20%, -50%) translateY(5px);
  }
  50% {
    transform: translate(0%, -50%) translateY(0);
  }
  60% {
    transform: translate(20%, -50%) translateY(-5px);
  }
  70% {
    transform: translate(40%, -50%) translateY(0);
  }
  80% {
    transform: translate(60%, -50%) translateY(5px);
  }
  90% {
    opacity: 1;
    transform: translate(80%, -50%) translateY(0);
  }
  100% {
    opacity: 0;
    transform: translate(100%, -50%) translateY(-5px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-plane-right {
  animation: planeRight 4s ease-in-out infinite;
  animation-delay: 0.5s;
}

@keyframes singlePlaneAcross {
  0% {
    opacity: 0;
    transform: translate(-5%, -50%) translateY(5px);
  }
  5% {
    opacity: 1;
    transform: translate(0%, -50%) translateY(0);
  }
  10% {
    transform: translate(10%, -50%) translateY(-5px);
  }
  20% {
    transform: translate(20%, -50%) translateY(0);
  }
  30% {
    transform: translate(30%, -50%) translateY(5px);
  }
  40% {
    transform: translate(40%, -50%) translateY(0);
  }
  50% {
    transform: translate(50%, -50%) translateY(-5px);
  }
  60% {
    transform: translate(60%, -50%) translateY(0);
  }
  70% {
    transform: translate(70%, -50%) translateY(5px);
  }
  80% {
    transform: translate(80%, -50%) translateY(0);
  }
  90% {
    transform: translate(90%, -50%) translateY(-5px);
  }
  95% {
    opacity: 1;
    transform: translate(95%, -50%) translateY(0);
  }
  100% {
    opacity: 0;
    transform: translate(100%, -50%) translateY(5px);
  }
}

.single-plane-animation {
  animation: singlePlaneAcross 8s ease-in-out infinite;
  animation-delay: 1s;
}

.animate-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}
